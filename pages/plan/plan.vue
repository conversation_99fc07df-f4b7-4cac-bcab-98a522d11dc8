<template>
	<view class="page-container">
		<!-- 支付/使用确认弹窗 -->
		<u-popup :round="16" mode="center" closeOnClickOverlay bgColor="transparent" :show="show" @close="close"
			@open="open">
			<view class="picbox"
				style="background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/pop-bg.png');">
				<view class="closeicon" @tap="close">
					<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-cancelbtn.png"></image>
				</view>
				<view class="successimg" style="position: absolute;left:50%;margin-left:-90rpx;top:-10rpx;z-index:99;">
					<image style="width:220rpx;" mode="widthFix"
						src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/popicon1.png"></image>
				</view>
				<view class="myboxs">
					<view class="cont">
						<text v-if="confirmText=='微信支付'">您确定花费{{itemInfo.money}}元\n购买<text
								class="bold">{{itemInfo.name}}</text>{{itemInfo.total}}次吗？</text>
						<text v-if="confirmText=='立即使用'">您已购买<text
								class="bold">{{itemInfo.name}}</text>{{itemInfo.total}}次!</text>
					</view>
					<view class="btn"><button :disabled="disabled" @tap="confirmPay">{{confirmText}}</button></view>
				</view>
			</view>
		</u-popup>

		<!-- 背景图案 -->
		<view class="bgs">
			<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-bg11.png" mode="aspectFit"
				style="position: absolute;left:0;top:0;width:100%;height:60vh;"></image>
		</view>

		<!-- 页面头部 -->
		<view class="head" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="text">规划报告</view>
			</view>
		</view>

		<!-- 加载动画 -->
		<view class="loading-mask" v-if="loading">
			<view class="loading-spinner"></view>
		</view>

		<!-- 下拉刷新区域 -->
		<scroll-view scroll-y @refresherpulling="onPulling" @refresherrefresh="onRefresh"
			:refresher-enabled="true" :refresher-triggered="isRefreshing"
			class="scroll-container">
			<!-- 添加顶部空白区域，避免内容被头部遮挡 -->
			<view style="height: 20rpx;"></view>

			<!-- 列表内容 -->
			<view class="list">
				<!-- 无数据状态 -->
				<view class="none" v-if="list.length==0">
					<view class="icon">
						<image :src="imgUrl+'/qdkbm/newimage/nodata-express.png'"></image>
					</view>
					<view class="text">暂无数据</view>
				</view>

				<!-- 列表项 -->
				<view class="item" v-for="(item,index) in list" :data-name="item.name" :data-end="item.leftTimes"
					:data-total="item.totalTimes" :data-money="item.price" :data-id="item.id" :key="index" @tap="clickDetail">
					<block v-if="item.id==1">
						<image style="width: 100%;" src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-box1.png"
							mode="widthFix"></image>
					</block>
					<block v-if="item.id==2">
						<image style="width: 100%;" src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-box2.png"
							mode="widthFix"></image>
					</block>
					<view class="boxs">
						<view class="box1">
							<view class="left">{{item.name}}</view>
							<view class="right">可用次数：{{item.leftTimes}}/{{item.totalTimes}}</view>
						</view>
						<view class="box2">
							{{item.content}}
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 底部导航栏 -->
		<tabBar :current-page="2"></tabBar>
	</view>
</template>

<script>
	import tabBar from '@/components/tabBar.vue'
	export default {
		components: {
			tabBar
		},
		data() {
			return {
				disabled: false,
				loading: false,
				isRefreshing: false, // 下拉刷新状态
				imgUrl: this.$base.uploadImgUrl,
				itemInfo: {},
				show: false,
				confirmText: "",
				myMsg: "",
				list: [],
				orderId: "",
				openId: "",
				titleTop: 0
			}
		},
		onLoad() {
			// 页面加载时初始化数据
			this.initData()
		},
		onShow() {
			if (uni.getStorageSync('openId')) {
				this.openId = uni.getStorageSync('openId')
			}
			if(uni.getStorageSync('token')){
				this.getList()
			} else{
				// 未登录状态下显示默认数据
				this.setDefaultList()
			}
		},
		onReady() {
			// 获取状态栏高度以调整页面布局
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {
			// 初始化数据
			initData() {
				// 显示加载动画
				uni.showLoading({
					title: '加载中...',
					mask: true
				})

				// 如果已登录则获取数据，否则设置默认数据
				if(uni.getStorageSync('token')) {
					this.getList()
				} else {
					this.setDefaultList()
					uni.hideLoading()
				}
			},

			// 设置默认列表数据
			setDefaultList() {
				this.list = [
					{
						id: 1,
						name: "体验版",
						price: 9.9,
						leftTimes: 0,
						totalTimes: 0,
						content: "根据最新教育部高考大数据，给您提供1份个性化院校及专业填报指南。"
					},
					{
						id: 2,
						name: "专业版",
						leftTimes: 0,
						price: 399,
						totalTimes: 0,
						content: "堪比志愿名师标准，给你生成20+份精准分析报告，内容含院校及专业填报推荐，学校特色，专业等。"
					}
				]
			},

			// 下拉刷新触发
			onRefresh() {
				this.isRefreshing = true

				// 刷新数据
				if(uni.getStorageSync('token')) {
					this.getList(true)
				} else {
					this.setDefaultList()
					setTimeout(() => {
						this.isRefreshing = false
					}, 800)
				}
			},

			// 下拉中
			onPulling(e) {
				// 可以根据下拉距离添加一些动画效果
			},

			// 获取列表数据
			getList(isRefresh = false) {
				// 如果不是刷新操作，则显示加载动画
				if(!isRefresh) {
					this.loading = true
				}

				this.$apis.getWishIntroduce({source:'tab'}).then((res) => {
					if (res.code == 0) {
						this.list = res.data || []

						// 如果没有数据，设置默认列表
						if(!res.data || res.data.length === 0){
							this.setDefaultList()
						}
					} else {
						// 请求失败，显示空列表
						this.list = []
						uni.showToast({
							title: '获取数据失败',
							icon: 'none'
						})
					}

					// 延迟关闭加载状态，提升用户体验
					setTimeout(() => {
						this.loading = false
						this.isRefreshing = false
						uni.hideLoading()
					}, 500)
				}).catch((err) => {
					// 请求异常处理
					console.error('获取数据失败:', err)
					this.list = []
					uni.showToast({
						title: '网络异常，请稍后重试',
						icon: 'none'
					})

					setTimeout(() => {
						this.loading = false
						this.isRefreshing = false
						uni.hideLoading()
					}, 500)
				})
			},

			confirmbtn2() {
				let that = this
				that.show = false
				let version = that.itemInfo.name === '体验版' ? 1 : 2
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/plan/work?ver=' + version
					})
				}, 200)
			},
			confirmPay() {
				if (this.confirmText == '微信支付') {
					this.disabled = true
					this.$apis.createOrder({
						items: [{
							skuId: this.itemInfo.id == 1 ? 30 : 31,
							count: 1,
							cartId: null
						}],
						couponId: undefined,
						pointStatus: false,
						deliveryType: 2,
						addressId: undefined,
						pickUpStoreId: undefined,
						receiverName: "",
						receiverMobile: "",
						seckillActivityId: undefined,
						combinationActivityId: undefined,
						combinationHeadId: undefined,
						bargainRecordId: undefined,
						pointActivityId: undefined,
						remark: ""
					}).then((res) => {
						if (res.code == 0) {
							this.orderId = res.data.payOrderId
							this.wechatPay()
						} else{
							this.disabled = false
						}
					}).catch((err)=>{
						this.disabled = false
					})
				} else {
					this.getList()
					this.confirmbtn2()
				}
			},
			wechatPay() {
				this.$apis.submitOrder({
					id: this.orderId,
					channelCode: "wx_pub",
					channelExtras: {
						openid: this.openId
					},
					displayMode: "url",
					returnUrl: ""
				}).then((res) => {
					if (res.code == 0) {
						let obj = JSON.parse(res.data.displayContent)
						this.wepay(obj)
					} else{
						this.disabled = false
					}
				}).catch((err)=>{
					this.disabled = false
				})
			},
			wepay(obj) {
				let that = this
				uni.requestPayment({
					appId: obj.appId,
					timeStamp: obj.timeStamp,
					nonceStr: obj.nonceStr,
					package: obj.packageValue,
					signType: obj.signType,
					paySign: obj.paySign,
					success(res) {
						that.disabled = false
						uni.showToast({
							title: "支付成功",
							icon: "none",
							duration: 1000
						})
						setTimeout(() => {
							that.myMsg = '您已购买' + that.itemInfo.name + that.itemInfo.totalTimes?that.itemInfo.totalTimes:10 + '次！';
							that.confirmText = "立即使用"
						}, 1000)
					},
					fail(res) {
						that.disabled = false
						uni.showToast({
							title: '支付失败',
							icon: 'none',
							duration: 650
						})
					}
				});
			},
			open() {
				this.show = true
			},
			close() {
				this.show = false
				this.disabled = false
			},
			async clickDetail(e) {
				let count = e.currentTarget.dataset.end
				let itemInfo = {
					name: e.currentTarget.dataset.name,
					id:e.currentTarget.dataset.id,
					count:e.currentTarget.dataset.end,
					total: e.currentTarget.dataset.name === '体验版'?1:3,
					money: e.currentTarget.dataset.money
				}
				let version = itemInfo.name === '体验版' ? 1 : 2

				let token = uni.getStorageSync('token')
				if(token){
					// 从接口获取用户信息
					let userId = uni.getStorageSync('userId')
					if (!userId) {
						uni.showModal({
							title: '提示',
							content: '用户信息不完整，请重新登录',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/login/login'
									});
								}
							}
						});
						return;
					}

					// 显示加载中
					uni.showLoading({
						title: '获取用户信息...',
						mask: true
					});

					// 调用接口获取用户信息
					uni.request({
						url: this.$base.baseUrl + 'admin-api/system/user/get?id=' + userId,
						method: 'GET',
						header: {
							'token': uni.getStorageSync('token')
						},
						success: (res) => {
							uni.hideLoading();
							console.log('接口获取的用户信息:', res.data)

							if (res.data.code === 0 && res.data.data) {
								let myInfo = res.data.data;

								// 如果没有填写分数
								if(!myInfo.score || myInfo.score === 0) {
									uni.showModal({
										title: '提示',
										content: '您还未填写高考分数，请先在个人中心完善信息',
										confirmText: '去填写',
										success: (res) => {
											if (res.confirm) {
												// 获取用户信息并传递给编辑页面
												uni.navigateTo({
													url: '/pages/center/edit-profile?item=' + encodeURIComponent(JSON.stringify(myInfo))
												});
											}
										}
									});
									return;
								}

								// 如果有分数但没有推荐专业
								if(myInfo.score && (!myInfo.recommendedMajors || myInfo.recommendedMajors === '')) {
									uni.showModal({
										title: '提示',
										content: '正在为您筛选合适的院校与专业，请稍后再来',
										showCancel: false,
										confirmText: '知道了'
									});
									return;
								}

								// 存储用户信息到本地
								uni.setStorageSync('myInfo', myInfo);

								// 继续处理
								uni.navigateTo({
									url: '/pages/plan/work?ver=' + version +'&item=' + encodeURIComponent(JSON.stringify(itemInfo))
								});
							} else {
								uni.showModal({
									title: '提示',
									content: '获取用户信息失败，请重试',
									showCancel: false
								});
							}
						},
						fail: (err) => {
							this.loading = false;
							console.error('获取用户信息失败:', err);
							uni.showModal({
								title: '提示',
								content: '网络错误，请检查网络连接后重试',
								showCancel: false
							});
						}
					});
				} else{
					// 显示加载中
					this.loading = true;

					// 尝试自动登录
					let loginRes = await this.$store.dispatch('gologinPage')
					if (loginRes) {
						// 自动登录成功后，获取最新的用户信息
						let userId = uni.getStorageSync('userId');
						if (!userId) {
							this.loading = false;
							uni.showModal({
								title: '提示',
								content: '用户信息不完整，请重新登录',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										uni.navigateTo({
											url: '/pages/login/login'
										});
									}
								}
							});
							return;
						}

						// 调用接口获取用户信息
						uni.request({
							url: this.$base.baseUrl + 'admin-api/system/user/get?id=' + userId,
							method: 'GET',
							header: {
								'token': uni.getStorageSync('token')
							},
							success: (res) => {
								this.loading = false;
								console.log('自动登录后获取的用户信息:', res.data);

								if (res.data.code === 0 && res.data.data) {
									let myInfo = res.data.data;

									// 如果没有填写分数
									if(!myInfo.score || myInfo.score === 0) {
										uni.showModal({
											title: '提示',
											content: '您还未填写高考分数，请先在个人中心完善信息',
											confirmText: '去填写',
											success: (res) => {
												if (res.confirm) {
													// 获取用户信息并传递给编辑页面
													uni.navigateTo({
														url: '/pages/center/edit-profile?item=' + encodeURIComponent(JSON.stringify(myInfo))
													});
												}
											}
										});
										return;
									}

									// 如果有分数但没有推荐专业
									if(myInfo.score && (!myInfo.recommendedMajors || myInfo.recommendedMajors === '')) {
										uni.showModal({
											title: '提示',
											content: '正在为您筛选合适的院校与专业，请稍后再来',
											showCancel: false,
											confirmText: '知道了'
										});
										return;
									}

									// 存储用户信息到本地
									uni.setStorageSync('myInfo', myInfo);

									// 继续处理
									uni.navigateTo({
										url: '/pages/plan/work?ver=' + version +'&item=' + encodeURIComponent(JSON.stringify(itemInfo))
									});
								} else {
									uni.showModal({
										title: '提示',
										content: '获取用户信息失败，请重试',
										showCancel: false
									});
								}
							},
							fail: (err) => {
								this.loading = false;
								console.error('获取用户信息失败:', err);
								uni.showModal({
									title: '提示',
									content: '网络错误，请检查网络连接后重试',
									showCancel: false
								});
							}
						});
					} else {
						// 自动登录失败，不做任何处理
						// 用户可以继续使用应用，但无法进入下一步
						this.loading = false;
					}
				}
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	}
</script>

<style>
@import './plan.css';
</style>
