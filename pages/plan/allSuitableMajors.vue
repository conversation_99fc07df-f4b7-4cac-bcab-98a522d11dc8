<template>
	<view class="container">
		<!-- 头部 -->
		<view class="header" :style="'padding-top:' + (titleTop + 10) + 'px'">
			<view class="header-content">
				<view class="back-btn" @tap="back">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'" mode="widthFix"></image>
				</view>
				<view class="header-title">适合专业推荐</view>
				<view class="header-right">
					<view class="more-btn">
						<text>···</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stats-header">
				<text class="university-name">{{userInfo.province || '安徽'}}适合专业</text>
				<text class="university-code">{{totalCount}}个专业</text>
				<text class="match-rate">匹配 96%</text>
			</view>
			<view class="stats-numbers">
				<view class="stat-item">
					<text class="stat-number total">全部 {{totalCount}}</text>
				</view>
				<view class="stat-item">
					<text class="stat-number rush">冲 {{rushCount}}</text>
				</view>
				<view class="stat-item">
					<text class="stat-number stable">稳 {{stableCount}}</text>
				</view>
				<view class="stat-item">
					<text class="stat-number safe">保 {{safeCount}}</text>
				</view>
			</view>
		</view>

		<!-- 筛选按钮 -->
		<view class="filter-tabs">
			<view class="tab-item" :class="{active: currentFilter === 'all'}" @tap="filterByCategory('all')">
				<text>全部</text>
				<text class="tab-count">{{totalCount}}</text>
			</view>
			<view class="tab-item" :class="{active: currentFilter === 'rush'}" @tap="filterByCategory('rush')">
				<text>冲</text>
				<text class="tab-count">{{rushCount}}</text>
			</view>
			<view class="tab-item" :class="{active: currentFilter === 'stable'}" @tap="filterByCategory('stable')">
				<text>稳</text>
				<text class="tab-count">{{stableCount}}</text>
			</view>
			<view class="tab-item" :class="{active: currentFilter === 'safe'}" @tap="filterByCategory('safe')">
				<text>保</text>
				<text class="tab-count">{{safeCount}}</text>
			</view>
		</view>

		<!-- 专业列表 -->
		<view class="major-list">
			<!-- 空状态 -->
			<view class="empty-state" v-if="!loading && majorList.length === 0">
				<view class="empty-icon">📚</view>
				<view class="empty-text">暂无专业数据</view>
				<view class="empty-tip">请检查网络连接或稍后重试</view>
			</view>

			<view class="major-item" v-for="(item, index) in majorList" :key="index" @tap="viewMajorDetail(item)">
				<view class="major-header">
					<view class="school-info">
						<view class="school-details">
							<view class="school-name">{{item.schoolName}}</view>
							<view class="school-meta">
								<text class="school-location">{{item.province}} {{item.schoolType}}</text>
								<text class="major-code">{{item.majorCode}}</text>
							</view>
						</view>
					</view>
					<view class="recommendation-badge" v-if="item.category" :style="'background-color: ' + item.categoryColor">
						<text class="badge-label">{{item.category}}</text>
					</view>
				</view>

				<view class="major-content">
					<view class="major-name">{{item.majorName}}</view>
					<view class="major-group">[{{item.majorStandardCode || item.majorCode}}]专业组</view>

					<view class="major-stats">
						<view class="stat-row">
							<text class="stat-label">年份</text>
							<text class="stat-label">招生</text>
							<text class="stat-label">最低分</text>
							<text class="stat-label">最低位次</text>
							<text class="stat-label">专业分</text>
						</view>
						<view class="stat-row" v-for="(year, yearIndex) in item.yearData" :key="yearIndex">
							<text class="stat-value">{{year.year}}</text>
							<text class="stat-value">{{year.enrollment}}人</text>
							<text class="stat-value">{{year.minScore}}</text>
							<text class="stat-value">{{year.minRank}}</text>
							<text class="stat-value">{{year.majorScore}}</text>
						</view>
					</view>
				</view>

				<view class="major-footer">
					<view class="major-tags">
						<text class="tag-item" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
					</view>
					<view class="action-btn" @tap.stop="toggleFavorite(item)">
						<text>可填专业 {{item.fillableCount}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore" @tap="loadMore">
			<text>加载更多</text>
		</view>

		<!-- 加载中 -->
		<view class="loading-mask" v-if="loading">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	// 启用下拉刷新
	onPullDownRefresh() {
		this.refreshData();
	},

	data() {
		return {
			imgUrl: this.$base.uploadImgUrl,
			titleTop: 0,
			loading: false,
			hasMore: true,
			pageNo: 1,
			pageSize: 10,

			// 用户信息
			userInfo: {},
			userProfile: {},

			// 统计数据
			universityCode: '9005',
			matchRate: 96,
			totalCount: 23,
			rushCount: 5,
			stableCount: 4,
			safeCount: 14,

			// 专业列表
			majorList: [],
			allMajorList: [], // 存储所有专业数据

			// 问卷答案数据
			questionData: null,

			// 当前筛选类型
			currentFilter: 'all'
		}
	},

	onLoad(options) {
		// 获取传递的问卷数据
		if (options.questionData) {
			try {
				this.questionData = JSON.parse(decodeURIComponent(options.questionData));
			} catch (e) {
				console.error('解析问卷数据失败:', e);
			}
		}

		// 获取报告ID
		if (options.reportId) {
			this.reportId = options.reportId;
		}
	},

	onReady() {
		let res = uni.getMenuButtonBoundingClientRect();
		this.titleTop = res.top;
	},

	onShow() {
		this.getUserInfo();
		this.getMajorData();
	},

	methods: {
		// 获取用户信息
		getUserInfo() {
			const userInfo = this.$store.state.userInfo;
			if (userInfo) {
				this.userInfo = userInfo;
			}
		},

		// 获取专业数据
		getMajorData() {
			if (!this.questionData) {
				// 如果没有问卷数据，尝试从报告ID获取
				this.getQuestionDataFromReport();
				return;
			}

			// 提取 userAnswers 对象
			let userAnswers = null;
			if (this.questionData.question && this.questionData.question.userAnswers) {
				userAnswers = this.questionData.question.userAnswers;
			} else if (this.questionData.userAnswers) {
				userAnswers = this.questionData.userAnswers;
			} else {
				console.error('无法找到 userAnswers 数据:', this.questionData);
				uni.showToast({
					title: '问卷数据格式错误',
					icon: 'none'
				});
				return;
			}

			console.log('准备调用接口，userAnswers:', userAnswers);

			this.loading = true;

			// 调用接口时只传递 userAnswers 对象
			this.$apis.getAllSuitableMajors({
				userAnswers: userAnswers
			}).then((res) => {
				this.loading = false;
				console.log('接口返回结果:', res);
				if (res.code === 0 && res.data) {
					this.processMajorData(res.data);
				} else {
					console.error('接口返回错误:', res);
					uni.showToast({
						title: res.msg || '获取专业数据失败',
						icon: 'none'
					});
				}
			}).catch((err) => {
				this.loading = false;
				console.error('获取专业数据失败:', err);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			});
		},

		// 从报告ID获取问卷数据
		getQuestionDataFromReport() {
			if (!this.reportId) {
				uni.showToast({
					title: '缺少必要参数',
					icon: 'none'
				});
				return;
			}

			// 这里需要调用获取问卷答案的接口
			this.$apis.getRecordAnswer({
				answerRecordId: this.reportId
			}).then((res) => {
				if (res.code === 0 && res.data) {
					this.questionData = res.data;
					this.getMajorData();
				}
			}).catch((err) => {
				console.error('获取问卷数据失败:', err);
			});
		},

		// 处理专业数据
		processMajorData(data) {
			console.log('处理专业数据:', data);

			// 更新统计数据
			if (data.totalCount !== undefined) {
				this.totalCount = data.totalCount;
			}
			if (data.rushCount !== undefined) {
				this.rushCount = data.rushCount;
			}
			if (data.stableCount !== undefined) {
				this.stableCount = data.stableCount;
			}
			if (data.safeCount !== undefined) {
				this.safeCount = data.safeCount;
			}

			// 更新用户信息
			if (data.userProfile) {
				this.userProfile = data.userProfile;
				// 更新省份信息
				if (data.userProfile.province) {
					this.userInfo.province = data.userProfile.province;
				}
			}

			// 合并所有专业数据
			let allMajors = [];

			// 添加冲刺专业
			if (data.rushMajors && Array.isArray(data.rushMajors)) {
				allMajors = allMajors.concat(data.rushMajors.map(item => ({
					...item,
					category: '冲',
					categoryColor: '#FF6B6B',
					schoolName: item.SchoolName,
					majorName: item.MajorName,
					majorCode: item.MajorCode,
					province: item.ProvinceName,
					schoolType: item.BatchName,
					minScore: item.LowestScore,
					minRank: item.LowestSection,
					year: item.Year,
					typeName: item.TypeName,
					proScore: item.ProScore,
					averageScore: item.AverageScore,
					highScore: item.HighSocre,
					majorStandardCode: item.MajorStandardCode,
					schoolUUID: item.SchoolUUID,
					enrollmentPlan: item.enrollmentPlanData || [],
					isRecommended: true,
					recommendLevel: 20
				})));
			}

			// 添加稳妥专业
			if (data.stableMajors && Array.isArray(data.stableMajors)) {
				allMajors = allMajors.concat(data.stableMajors.map(item => ({
					...item,
					category: '稳',
					categoryColor: '#4ECDC4',
					schoolName: item.SchoolName,
					majorName: item.MajorName,
					majorCode: item.MajorCode,
					province: item.ProvinceName,
					schoolType: item.BatchName,
					minScore: item.LowestScore,
					minRank: item.LowestSection,
					year: item.Year,
					typeName: item.TypeName,
					proScore: item.ProScore,
					averageScore: item.AverageScore,
					highScore: item.HighSocre,
					majorStandardCode: item.MajorStandardCode,
					schoolUUID: item.SchoolUUID,
					enrollmentPlan: item.enrollmentPlanData || [],
					isRecommended: true,
					recommendLevel: 15
				})));
			}

			// 添加保底专业
			if (data.safeMajors && Array.isArray(data.safeMajors)) {
				allMajors = allMajors.concat(data.safeMajors.map(item => ({
					...item,
					category: '保',
					categoryColor: '#45B7D1',
					schoolName: item.SchoolName,
					majorName: item.MajorName,
					majorCode: item.MajorCode,
					province: item.ProvinceName,
					schoolType: item.BatchName,
					minScore: item.LowestScore,
					minRank: item.LowestSection,
					year: item.Year,
					typeName: item.TypeName,
					proScore: item.ProScore,
					averageScore: item.AverageScore,
					highScore: item.HighSocre,
					majorStandardCode: item.MajorStandardCode,
					schoolUUID: item.SchoolUUID,
					enrollmentPlan: item.enrollmentPlanData || [],
					isRecommended: false,
					recommendLevel: 10
				})));
			}

			// 为每个专业添加标签和其他信息
			this.allMajorList = allMajors.map(item => ({
				...item,
				id: item.MajorCode || item.majorCode,
				tags: this.generateTags(item),
				fillableCount: item.enrollmentPlan ? item.enrollmentPlan.length : 1,
				yearData: this.generateYearData(item)
			}));

			// 初始显示所有专业
			this.majorList = this.allMajorList;

			console.log('处理后的专业列表:', this.allMajorList);
		},

		// 生成标签
		generateTags(item) {
			const tags = [];

			// 添加省份
			if (item.province) {
				tags.push(item.province);
			}

			// 添加选科类型
			if (item.typeName) {
				tags.push(item.typeName);
			}

			// 添加分数信息
			if (item.minScore && item.minScore !== '-') {
				tags.push(`${item.minScore}分`);
			}

			// 添加位次信息
			if (item.minRank && item.minRank !== '-') {
				tags.push(`${item.minRank}名`);
			}

			return tags;
		},

		// 生成年份数据
		generateYearData(item) {
			const yearData = [];

			// 使用历年录取分数数据 historicalData
			if (item.historicalData && Array.isArray(item.historicalData)) {
				item.historicalData.forEach(historyItem => {
					// 查找对应年份的招生计划数据
					let enrollmentInfo = '-';
					if (item.enrollmentPlanData && Array.isArray(item.enrollmentPlanData)) {
						const planItem = item.enrollmentPlanData.find(plan => plan.Year == historyItem.year);
						if (planItem && planItem.EnrollmentNumbers) {
							enrollmentInfo = `${planItem.EnrollmentNumbers}人`;
						}
					}

					yearData.push({
						year: historyItem.year,
						enrollment: enrollmentInfo,
						minScore: historyItem.lowestScore || '-',
						minRank: historyItem.lowestSection || '-',
						majorScore: item.ProScore || '-'
					});
				});
			}

			// 如果没有历年数据，使用当前年份数据
			if (yearData.length === 0 && item.year) {
				let enrollmentInfo = '-';
				if (item.enrollmentPlanData && Array.isArray(item.enrollmentPlanData)) {
					const currentYearPlan = item.enrollmentPlanData.find(plan => plan.Year == item.year);
					if (currentYearPlan && currentYearPlan.EnrollmentNumbers) {
						enrollmentInfo = `${currentYearPlan.EnrollmentNumbers}人`;
					}
				}

				yearData.push({
					year: item.year,
					enrollment: enrollmentInfo,
					minScore: item.LowestScore || '-',
					minRank: item.LowestSection || '-',
					majorScore: item.ProScore || '-'
				});
			}

			// 如果仍然没有数据，添加默认的三年数据结构
			if (yearData.length === 0) {
				yearData.push(
					{ year: 2024, enrollment: '-', minScore: '-', minRank: '-', majorScore: '-' },
					{ year: 2023, enrollment: '-', minScore: '-', minRank: '-', majorScore: '-' },
					{ year: 2022, enrollment: '-', minScore: '-', minRank: '-', majorScore: '-' }
				);
			}

			// 按年份降序排序
			yearData.sort((a, b) => b.year - a.year);

			return yearData;
		},



		// 按分类筛选
		filterByCategory(category) {
			this.currentFilter = category;

			// 将英文类型转换为中文类型进行筛选
			const categoryMap = {
				'rush': '冲',
				'stable': '稳',
				'safe': '保'
			};

			if (category === 'all') {
				this.majorList = this.allMajorList;
			} else {
				let targetCategory = categoryMap[category] || category;
				this.majorList = this.allMajorList.filter(item => item.category === targetCategory);
			}

			console.log('筛选结果:', category, '目标分类:', categoryMap[category], '结果数量:', this.majorList.length);
			console.log('所有专业的分类:', this.allMajorList.map(item => item.category));
		},

		// 查看专业详情
		viewMajorDetail(item) {
			uni.navigateTo({
				url: `/pages/plan/majorDetail?majorId=${item.id}&majorName=${encodeURIComponent(item.majorName)}`
			});
		},

		// 切换收藏
		toggleFavorite(item) {
			// 实现收藏功能
			console.log('切换收藏:', item);
		},

		// 加载更多
		loadMore() {
			if (this.loading || !this.hasMore) return;

			this.pageNo++;
			this.getMajorData();
		},

		// 下拉刷新
		refreshData() {
			this.pageNo = 1;
			this.hasMore = true;
			this.majorList = [];
			this.getMajorData();

			// 停止下拉刷新动画
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},

		// 返回
		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
}
</script>

<style scoped lang="scss">
page {
	background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
}

.container {
	background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
	min-height: 100vh;
}

.header {
	background: linear-gradient(135deg, #FF8918 0%, #FF6B18 100%);
	box-shadow: 0 4rpx 20rpx rgba(255, 137, 24, 0.3);

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;

		.back-btn {
			width: 44rpx;
			height: 44rpx;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 22rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.9);
				background: rgba(255, 255, 255, 0.3);
			}

			image {
				width: 24rpx;
				height: 24rpx;
				filter: brightness(0) invert(1);
			}
		}

		.header-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #fff;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		}

		.header-right {
			width: 44rpx;
			height: 44rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.more-btn {
				font-size: 32rpx;
				color: rgba(255, 255, 255, 0.8);
				font-weight: bold;
			}
		}
	}
}



.stats-section {
	background: #fff;
	padding: 30rpx;
	margin: 20rpx 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 137, 24, 0.1);

	.stats-header {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
		flex-wrap: wrap;
		gap: 12rpx;

		.university-name {
			font-size: 32rpx;
			font-weight: bold;
			color: #2c3e50;
			background: linear-gradient(135deg, #FF8918 0%, #FF6B18 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.university-code {
			font-size: 24rpx;
			color: #7f8c8d;
			background: #f8f9fa;
			padding: 4rpx 12rpx;
			border-radius: 12rpx;
		}

		.match-rate {
			font-size: 24rpx;
			color: #fff;
			background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
			padding: 6rpx 16rpx;
			border-radius: 16rpx;
			font-weight: 500;
		}
	}

	.stats-numbers {
		display: flex;
		justify-content: space-between;
		gap: 16rpx;

		.stat-item {
			flex: 1;
			text-align: center;

			.stat-number {
				font-size: 28rpx;
				font-weight: 600;
				padding: 12rpx 16rpx;
				border-radius: 16rpx;
				display: block;
				transition: all 0.3s ease;

				&.total {
					color: #fff;
					background: linear-gradient(135deg, #FF8918 0%, #FF6B18 100%);
					box-shadow: 0 4rpx 16rpx rgba(255, 137, 24, 0.3);
				}

				&.rush {
					color: #fff;
					background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
					box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
				}

				&.stable {
					color: #fff;
					background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
					box-shadow: 0 4rpx 16rpx rgba(78, 205, 196, 0.3);
				}

				&.safe {
					color: #fff;
					background: linear-gradient(135deg, #45b7d1 0%, #2980b9 100%);
					box-shadow: 0 4rpx 16rpx rgba(69, 183, 209, 0.3);
				}
			}
		}
	}
}

.filter-tabs {
	display: flex;
	background: #fff;
	padding: 8rpx;
	margin: 0 20rpx 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);

	.tab-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 16rpx 12rpx;
		border-radius: 12rpx;
		transition: all 0.3s ease;
		position: relative;

		text {
			font-size: 28rpx;
			font-weight: 600;
			color: #666;
			margin-bottom: 4rpx;
		}

		.tab-count {
			font-size: 22rpx;
			color: #999;
			font-weight: 500;
		}

		&.active {
			background: linear-gradient(135deg, #FF8918 0%, #FF6B18 100%);
			box-shadow: 0 4rpx 12rpx rgba(255, 137, 24, 0.3);

			text {
				color: #fff;
			}

			.tab-count {
				color: rgba(255, 255, 255, 0.8);
			}
		}

		&:active {
			transform: scale(0.95);
		}
	}
}

.major-list {
	padding: 0 20rpx;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	text-align: center;

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 30rpx;
		opacity: 0.6;
	}

	.empty-text {
		font-size: 32rpx;
		color: #666;
		font-weight: 500;
		margin-bottom: 16rpx;
	}

	.empty-tip {
		font-size: 28rpx;
		color: #999;
		line-height: 1.5;
	}
}

.major-item {
	background: #fff;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4rpx;
		background: linear-gradient(90deg, #FF8918 0%, #FF6B18 100%);
	}

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
	}
}

.major-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;

	.school-info {
		display: flex;
		align-items: center;
		flex: 1;

		.school-details {
			.school-name {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 8rpx;
			}

			.school-meta {
				display: flex;
				align-items: center;

				.school-location {
					font-size: 24rpx;
					color: #666;
					margin-right: 20rpx;
				}

				.major-code {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}

	.recommendation-badge {
		color: #fff;
		padding: 16rpx 24rpx;
		border-radius: 20rpx;
		text-align: center;
		min-width: 80rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
		position: relative;

		&::before {
			content: '';
			position: absolute;
			top: 2rpx;
			left: 2rpx;
			right: 2rpx;
			bottom: 2rpx;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 18rpx;
		}

		.badge-text {
			font-size: 24rpx;
			font-weight: bold;
		}

		.badge-label {
			font-size: 32rpx;
			font-weight: bold;
			line-height: 1;
			position: relative;
			z-index: 1;
		}
	}
}

.major-content {
	margin-bottom: 24rpx;

	.major-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #2c3e50;
		margin-bottom: 12rpx;
		line-height: 1.4;
	}

	.major-group {
		font-size: 26rpx;
		color: #7f8c8d;
		margin-bottom: 24rpx;
		background: #f8f9fa;
		padding: 8rpx 16rpx;
		border-radius: 12rpx;
		display: inline-block;
	}
}

.major-stats {
	.stat-row {
		display: flex;
		justify-content: space-between;
		padding: 8rpx 0;
		border-bottom: 1rpx solid #f5f5f5;

		&:last-child {
			border-bottom: none;
		}

		.stat-label {
			font-size: 24rpx;
			color: #666;
			flex: 1;
			text-align: center;
		}

		.stat-value {
			font-size: 24rpx;
			color: #333;
			flex: 1;
			text-align: center;
		}
	}
}

.major-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20rpx;

	.major-tags {
		display: flex;
		flex-wrap: wrap;
		flex: 1;

		.tag-item {
			background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
			color: #495057;
			padding: 8rpx 16rpx;
			border-radius: 12rpx;
			font-size: 22rpx;
			margin-right: 12rpx;
			margin-bottom: 8rpx;
			border: 1rpx solid #dee2e6;
			font-weight: 500;
		}
	}

	.action-btn {
		background: linear-gradient(135deg, #FF8918 0%, #FF6B18 100%);
		color: #fff;
		padding: 16rpx 28rpx;
		border-radius: 24rpx;
		font-size: 24rpx;
		font-weight: 600;
		box-shadow: 0 4rpx 16rpx rgba(255, 137, 24, 0.3);
		transition: all 0.3s ease;
		white-space: nowrap;

		&:active {
			transform: scale(0.95);
		}
	}
}

.load-more {
	text-align: center;
	padding: 40rpx;
	color: #7f8c8d;
	font-size: 28rpx;
	font-weight: 500;
}

.loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	backdrop-filter: blur(10rpx);

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: rgba(255, 255, 255, 0.95);
		border-radius: 24rpx;
		padding: 40rpx 50rpx;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
		border: 1rpx solid rgba(255, 255, 255, 0.8);

		.loading-spinner {
			width: 80rpx;
			height: 80rpx;
			animation: spin 1s linear infinite;
			background: linear-gradient(135deg, #FF8918 0%, #FF6B18 100%);
			border-radius: 50%;
			position: relative;

			&::before {
				content: '';
				position: absolute;
				top: 8rpx;
				left: 8rpx;
				right: 8rpx;
				bottom: 8rpx;
				background: rgba(255, 255, 255, 0.95);
				border-radius: 50%;
			}
		}

		.loading-text {
			margin-top: 24rpx;
			font-size: 30rpx;
			color: #2c3e50;
			font-weight: 600;
		}
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
